import json
import re
from typing import List, Dict, Any, Optional
from langchain_core.prompts import ChatPromptTemplate
from langchain_core.output_parsers import JsonOutputParser
from langchain_openai import ChatOpenAI
from langgraph.graph import StateGraph, END
from langgraph.graph.message import add_messages
from typing_extensions import TypedDict
from langchain_core.messages import HumanMessage, AIMessage

from backend.utils.file import load_json_file


class AgentState(TypedDict):
    """Agent 状态定义 - 根据设计思路扩展"""
    messages: List[Any]
    natural_language_input: str
    parsed_actions: List[Dict[str, Any]]
    character_info: Optional[Dict[str, Any]]
    scene_info: Optional[Dict[str, Any]]  # 新增：场景信息
    props_info: List[Dict[str, Any]]      # 新增：道具信息
    timeline_info: Optional[Dict[str, Any]]  # 新增：时间轴信息
    error: Optional[str]


class GameActionAgent:
    """游戏动作解析 Agent"""

    def __init__(self, model_name: str = "gpt-4o-mini", temperature: float = 0.1):
        """初始化 Agent

        Args:
            model_name: 使用的模型名称
            temperature: 模型温度参数
        """
        self.llm = ChatOpenAI(model=model_name, temperature=temperature)
        self.game_actions = load_json_file("backend/actions/game_action_command_set.json")
        self.parser = JsonOutputParser()

        # 创建 LangGraph
        self.graph = self._create_graph()

    def _create_action_schema_prompt(self) -> str:
        """创建动作模式的提示文本"""
        schema_text = "可用的游戏动作命令:\n\n"

        for action in self.game_actions:
            schema_text += f"**{action['name']} ({action['key']})**\n"
            schema_text += f"描述: {action['desc']}\n"
            schema_text += "参数:\n"

            for param, desc in action['params'].items():
                schema_text += f"  - {param}: {desc}\n"
            schema_text += "\n"

        return schema_text

    def _create_character_schema_prompt(self) -> str:
        """创建角色模式的提示文本"""
        try:
            character_types = load_json_file("backend/characters/character_types.json")
            schema_text = "可用的角色类型:\n\n"

            for char in character_types:
                schema_text += f"**{char['name']}** ({char['display_name']})\n"
                schema_text += f"描述: {char['description']}\n"
                schema_text += f"身高: {char['height']}m, 体型: {char['body_type']}\n"
                schema_text += f"动画特征: 行走风格={char['animation_features']['walking_style']}, "
                schema_text += f"跳跃风格={char['animation_features']['jump_style']}\n\n"

            return schema_text
        except Exception:
            return "角色类型: human_male, human_female, warrior, child, robot, fantasy_elf\n\n"

    def _create_system_prompt(self) -> str:
        """创建系统提示 - 根据设计思路增强"""
        action_schema = self._create_action_schema_prompt()
        character_schema = self._create_character_schema_prompt()

        return f"""你是一个专业的3D动画场景解析器。你的任务是将自然语言描述转换为完整的结构化信息，包括角色、动作、道具、场景和时间轴。

{character_schema}

{action_schema}

**解析规则:**
1. **角色识别**: 仔细分析角色类型、名称、状态和特征
2. **动作序列**: 识别所有动作，保持时间顺序和逻辑关系
3. **道具对象**: 识别提到的武器、工具、物品等
4. **场景环境**: 分析背景、环境、位置信息
5. **时间轴**: 处理动作的时间关系、持续时间、并发性
6. **依赖关系**: 识别动作之间的因果关系和条件

**Output Format:**
```json
{{{{
  "character": {{{{
    "type": "character_type_name",
    "name": "角色名称",
    "description": "角色描述",
    "initial_position": [x, y, z],
    "initial_orientation": [rx, ry, rz],
    "initial_state": "状态描述"
  }}}},
  "scene": {{{{
    "description": "场景描述",
    "environment": "环境类型",
    "lighting": "光照条件",
    "weather": "天气状况"
  }}}},
  "props": [
    {{{{
      "id": "prop_id",
      "name": "道具名称",
      "type": "道具类型",
      "owner_id": "持有者ID",
      "initial_position": [x, y, z]
    }}}}
  ],
  "timeline": {{{{
    "total_duration": duration_seconds,
    "concurrent_actions": boolean,
    "action_dependencies": [
      {{{{
        "action_id": "action1",
        "depends_on": ["action0"],
        "delay": delay_seconds
      }}}}
    ]
  }}}},
  "actions": [
    {{{{
      "id": "action_id",
      "action_type": "action_name",
      "action_key": "ACTION_KEY",
      "parameters": {{{{
        "param_name": "param_value"
      }}}},
      "sequence_number": number,
      "start_time": start_seconds,
      "duration": duration_seconds,
      "target_id": "target_object_or_character",
      "description_en": "English description",
      "description_zh": "中文描述"
    }}}}
  ]
}}}}
```

**Example 1:**
Input: "勇敢的战士举起盾牌防御，然后挥剑攻击敌人"
Output:
```json
{{{{
  "character": {{{{
    "type": "warrior",
    "name": "勇敢的战士",
    "description": "强壮的战士角色",
    "initial_position": [0, 0, 0],
    "initial_orientation": [0, 0, 0],
    "initial_state": "战斗准备"
  }}}},
  "scene": {{{{
    "description": "战场环境",
    "environment": "battlefield",
    "lighting": "daylight",
    "weather": "clear"
  }}}},
  "props": [
    {{{{
      "id": "shield_01",
      "name": "盾牌",
      "type": "shield",
      "owner_id": "warrior",
      "initial_position": [0, 0, 0]
    }}}},
    {{{{
      "id": "sword_01",
      "name": "剑",
      "type": "sword",
      "owner_id": "warrior",
      "initial_position": [0, 0, 0]
    }}}}
  ],
  "timeline": {{{{
    "total_duration": 3.5,
    "concurrent_actions": false,
    "action_dependencies": [
      {{{{
        "action_id": "attack_01",
        "depends_on": ["defend_01"],
        "delay": 0.2
      }}}}
    ]
  }}}},
  "actions": [
    {{{{
      "id": "defend_01",
      "action_type": "defend",
      "action_key": "DEFEND",
      "parameters": {{{{
        "type": "shield",
        "direction": "front"
      }}}},
      "sequence_number": 1,
      "start_time": 0.0,
      "duration": 1.5,
      "target_id": "shield_01",
      "description_en": "Raise shield for defense",
      "description_zh": "举起盾牌防御"
    }}}},
    {{{{
      "id": "attack_01",
      "action_type": "attack",
      "action_key": "ATTACK",
      "parameters": {{{{
        "weapon": "sword",
        "style": "slash",
        "strength": 0.8,
        "target": "enemy"
      }}}},
      "sequence_number": 2,
      "start_time": 1.7,
      "duration": 1.8,
      "target_id": "enemy",
      "description_en": "Swing sword to attack enemy",
      "description_zh": "挥剑攻击敌人"
    }}}}
  ]
}}}}
```

**Example 2:**
Input: "小女孩蹦蹦跳跳地向前跑"
Output:
```json
{{{{
  "character": {{{{
    "type": "child",
    "name": "小女孩",
    "description": "活泼的儿童角色"
  }}}},
  "actions": [
    {{{{
      "action_type": "move",
      "action_key": "MOVE",
      "parameters": {{{{
        "direction": "forward",
        "speed": 0.8,
        "duration": 3.0
      }}}},
      "sequence_number": 1,
      "description_en": "Run forward with bouncy style",
      "description_zh": "蹦蹦跳跳地向前跑"
    }}}}
  ]
}}}}
```

请严格按照上述格式输出，确保 JSON 格式正确且包含所有必要字段。"""

    def _create_graph(self) -> StateGraph:
        """创建 LangGraph 工作流"""
        workflow = StateGraph(AgentState)

        # 添加节点
        workflow.add_node("parse_input", self._parse_input_node)
        workflow.add_node("validate_output", self._validate_output_node)
        workflow.add_node("error_handler", self._error_handler_node)

        # 设置入口点
        workflow.set_entry_point("parse_input")

        # 添加边
        workflow.add_conditional_edges(
            "parse_input",
            self._should_validate,
            {
                "validate": "validate_output",
                "error": "error_handler"
            }
        )

        workflow.add_edge("validate_output", END)
        workflow.add_edge("error_handler", END)

        return workflow.compile()

    def _parse_input_node(self, state: AgentState) -> AgentState:
        """解析输入节点 - 增强版结构化信息提取"""
        try:
            # 创建提示模板
            prompt = ChatPromptTemplate.from_messages([
                ("system", self._create_system_prompt()),
                ("human", "请解析以下自然语言描述为完整的3D动画场景信息:\n\n{input}")
            ])

            # 创建链
            chain = prompt | self.llm | self.parser

            # 执行解析
            result = chain.invoke({"input": state["natural_language_input"]})

            # 处理增强的输出格式
            if isinstance(result, dict):
                # 角色信息
                if "character" in result:
                    state["character_info"] = result["character"]
                    char_name = result["character"].get("name", result["character"].get("type", "角色"))
                else:
                    state["character_info"] = {"type": "human_male", "name": "默认角色", "description": "默认人类角色"}
                    char_name = "默认角色"

                # 动作序列
                if "actions" in result:
                    state["parsed_actions"] = result["actions"]
                else:
                    state["parsed_actions"] = []

                # 场景信息
                if "scene" in result:
                    state["scene_info"] = result["scene"]
                else:
                    state["scene_info"] = {"description": "默认场景", "environment": "neutral"}

                # 道具信息
                if "props" in result:
                    state["props_info"] = result["props"]
                else:
                    state["props_info"] = []

                # 时间轴信息
                if "timeline" in result:
                    state["timeline_info"] = result["timeline"]
                else:
                    # 生成默认时间轴
                    total_duration = sum(action.get("duration", 1.0) for action in state["parsed_actions"])
                    state["timeline_info"] = {
                        "total_duration": total_duration,
                        "concurrent_actions": False,
                        "action_dependencies": []
                    }

                # 成功消息
                state["messages"].append(AIMessage(
                    content=f"成功解析完整场景: 角色'{char_name}', {len(state['parsed_actions'])}个动作, "
                           f"{len(state['props_info'])}个道具, 总时长{state['timeline_info']['total_duration']:.1f}秒"
                ))
            else:
                # 兼容旧格式
                state["parsed_actions"] = result if isinstance(result, list) else [result]
                state["character_info"] = {"type": "human_male", "name": "默认角色", "description": "默认人类角色"}
                state["scene_info"] = {"description": "默认场景", "environment": "neutral"}
                state["props_info"] = []
                state["timeline_info"] = {"total_duration": 5.0, "concurrent_actions": False, "action_dependencies": []}
                state["messages"].append(AIMessage(content=f"使用兼容模式解析了 {len(state['parsed_actions'])} 个动作"))

        except Exception as e:
            state["error"] = f"解析失败: {str(e)}"
            state["messages"].append(AIMessage(content=f"解析过程中发生错误: {str(e)}"))

        return state

    def _validate_output_node(self, state: AgentState) -> AgentState:
        """验证输出节点"""
        try:
            actions = state["parsed_actions"]

            # 验证每个动作的格式
            for i, action in enumerate(actions):
                if not self._validate_action_format(action):
                    raise ValueError(f"动作 {i+1} 格式不正确: {action}")

            state["messages"].append(AIMessage(content="动作序列验证通过"))

        except Exception as e:
            state["error"] = f"验证失败: {str(e)}"
            state["messages"].append(AIMessage(content=f"验证出错: {str(e)}"))

        return state

    def _error_handler_node(self, state: AgentState) -> AgentState:
        """错误处理节点"""
        error_msg = state.get("error", "未知错误")
        state["messages"].append(AIMessage(content=f"处理完成，但存在错误: {error_msg}"))
        state["parsed_actions"] = []
        return state

    def _should_validate(self, state: AgentState) -> str:
        """条件判断：是否需要验证"""
        if state.get("error"):
            return "error"
        return "validate"

    def _validate_action_format(self, action: Dict[str, Any]) -> bool:
        """验证单个动作的格式"""
        required_fields = ["action_type", "action_key", "parameters", "sequence_number", "description_en"]

        # 检查必需字段
        for field in required_fields:
            if field not in action:
                return False

        # 检查动作是否在支持列表中
        action_names = [a["name"] for a in self.game_actions]
        if action["action_type"] not in action_names:
            return False

        return True

    def parse_natural_language(self, natural_language: str) -> Dict[str, Any]:
        """解析自然语言为游戏动作命令

        Args:
            natural_language: 自然语言描述

        Returns:
            包含解析结果的字典
        """
        # 初始化状态
        initial_state = AgentState(
            messages=[HumanMessage(content=natural_language)],
            natural_language_input=natural_language,
            parsed_actions=[],
            character_info=None,
            scene_info=None,
            props_info=[],
            timeline_info=None,
            error=None
        )

        # 执行图
        result = self.graph.invoke(initial_state)

        # 返回结果
        return {
            "success": not bool(result.get("error")),
            "actions": result.get("parsed_actions", []),
            "character": result.get("character_info"),
            "error": result.get("error"),
            "input": natural_language
        }

    async def aparse_natural_language(self, natural_language: str) -> Dict[str, Any]:
        """异步解析自然语言为游戏动作命令

        Args:
            natural_language: 自然语言描述

        Returns:
            包含解析结果的字典
        """
        # 初始化状态
        initial_state = AgentState(
            messages=[HumanMessage(content=natural_language)],
            natural_language_input=natural_language,
            parsed_actions=[],
            character_info=None,
            scene_info=None,
            props_info=[],
            timeline_info=None,
            error=None
        )

        # 异步执行图
        result = await self.graph.ainvoke(initial_state)

        # 返回结果
        return {
            "success": not bool(result.get("error")),
            "actions": result.get("parsed_actions", []),
            "character": result.get("character_info"),
            "error": result.get("error"),
            "input": natural_language
        }

    def get_supported_actions(self) -> List[Dict[str, Any]]:
        """获取支持的动作列表"""
        return self.game_actions

    def validate_action_sequence(self, actions: List[Dict[str, Any]]) -> Dict[str, Any]:
        """验证动作序列的有效性

        Args:
            actions: 动作序列列表

        Returns:
            验证结果
        """
        errors = []
        warnings = []

        for i, action in enumerate(actions):
            # 验证格式
            if not self._validate_action_format(action):
                errors.append(f"动作 {i+1} 格式不正确")
                continue

            # 验证参数
            action_def = next((a for a in self.game_actions if a["name"] == action["action"]), None)
            if action_def:
                param_errors = self._validate_action_params(action["params"], action_def["params"])
                if param_errors:
                    errors.extend([f"动作 {i+1} 参数错误: {err}" for err in param_errors])

        return {
            "valid": len(errors) == 0,
            "errors": errors,
            "warnings": warnings
        }

    def _validate_action_params(self, params: Dict[str, Any], param_schema: Dict[str, str]) -> List[str]:
        """验证动作参数"""
        errors = []

        for param_name, param_value in params.items():
            if param_name not in param_schema:
                errors.append(f"未知参数: {param_name}")
                continue

            # 这里可以添加更详细的参数验证逻辑
            # 例如检查数值范围、枚举值等

        return errors