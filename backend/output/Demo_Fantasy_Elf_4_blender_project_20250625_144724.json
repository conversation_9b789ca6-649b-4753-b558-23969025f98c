{"project_name": "Demo_Fantasy_Elf_4", "scene": {"scene_name": "Demo_Fantasy_Elf_4_Scene", "frame_rate": 24.0, "frame_start": 1, "frame_end": 50, "objects": []}, "animation_clips": [{"name": "Demo_Fantasy_Elf_4_Animation", "start_frame": 1, "end_frame": 50, "frame_rate": 24.0, "channels": [{"target_object": "优雅的精灵", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 1, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 15, "value": [0.0, 0.0, 1.5], "interpolation": "EASE_OUT", "handle_left": null, "handle_right": null}, {"frame": 37, "value": [0.0, 0.0, 0.0], "interpolation": "EASE_IN", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "优雅的精灵", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 1, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 37, "value": [0.0, 1.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}], "markers": []}], "export_settings": {"export_format": "FBX", "export_path": "demo_fantasy_elf_4.fbx", "include_animations": true, "include_armatures": true, "include_meshes": true, "fbx_version": "7.4.0", "scale_factor": 1.0}, "metadata": {"source": "MotionAgent", "actions_count": 1, "total_frames": 50, "character": {"type": "fantasy_elf", "name": "优雅的精灵", "description": "优雅的精灵角色"}, "character_config": {"type": "fantasy_elf", "display_name": "精灵", "height": 1.75, "animation_features": {"has_facial_expressions": true, "has_hand_gestures": true, "walking_style": "light", "jump_style": "agile"}}}}