{"project_name": "Demo_Child_2", "scene": {"scene_name": "Demo_Child_2_Scene", "frame_rate": 24.0, "frame_start": 1, "frame_end": 121, "objects": []}, "animation_clips": [{"name": "Demo_Child_2_Animation", "start_frame": 1, "end_frame": 121, "frame_rate": 24.0, "channels": [{"target_object": "小女孩", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 1, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 73, "value": [0.0, 12.000000000000002, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "小女孩", "target_property": "rotation_euler", "animation_type": "rotation", "keyframes": [{"frame": 73, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 79, "value": [0.0, 0.5497787143782138, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 85, "value": [0.0, 6.732847428026263e-17, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 91, "value": [0.0, -0.5497787143782138, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 97, "value": [0.0, -1.3465694856052526e-16, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 103, "value": [0.0, 0.5497787143782138, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 109, "value": [0.0, 2.019854228407879e-16, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 115, "value": [0.0, -0.5497787143782138, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 121, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": "RightArm"}], "markers": []}], "export_settings": {"export_format": "FBX", "export_path": "demo_child_2.fbx", "include_animations": true, "include_armatures": true, "include_meshes": true, "fbx_version": "7.4.0", "scale_factor": 1.0}, "metadata": {"source": "MotionAgent", "actions_count": 2, "total_frames": 121, "character": {"type": "child", "name": "小女孩", "description": "活泼的儿童角色"}, "character_config": {"type": "child", "display_name": "儿童", "height": 1.2, "animation_features": {"has_facial_expressions": true, "has_hand_gestures": true, "walking_style": "bouncy", "jump_style": "playful"}}}}