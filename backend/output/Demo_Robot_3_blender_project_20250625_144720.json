{"project_name": "Demo_Robot_3", "scene": {"scene_name": "Demo_Robot_3_Scene", "frame_rate": 24.0, "frame_start": 1, "frame_end": 145, "objects": []}, "animation_clips": [{"name": "Demo_Robot_3_Animation", "start_frame": 1, "end_frame": 145, "frame_rate": 24.0, "channels": [{"target_object": "机器人", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 25, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 145, "value": [0.0, 7.5, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}], "markers": []}], "export_settings": {"export_format": "FBX", "export_path": "demo_robot_3.fbx", "include_animations": true, "include_armatures": true, "include_meshes": true, "fbx_version": "7.4.0", "scale_factor": 1.0}, "metadata": {"source": "MotionAgent", "actions_count": 2, "total_frames": 145, "character": {"type": "robot", "name": "机器人", "description": "机械角色"}, "character_config": {"type": "robot", "display_name": "机器人", "height": 2.0, "animation_features": {"has_facial_expressions": false, "has_hand_gestures": true, "walking_style": "mechanical", "jump_style": "robotic"}}}}