{"project_name": "Demo_Warrior_1", "scene": {"scene_name": "Demo_Warrior_1_Scene", "frame_rate": 24.0, "frame_start": 1, "frame_end": 56, "objects": []}, "animation_clips": [{"name": "Demo_Warrior_1_Animation", "start_frame": 1, "end_frame": 56, "frame_rate": 24.0, "channels": [{"target_object": "勇敢的战士", "target_property": "location", "animation_type": "location", "keyframes": [{"frame": 1, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 11, "value": [0.0, 0.0, -0.3], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 27, "value": [0.0, 0.0, -0.3], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 37, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}], "bone_name": null}, {"target_object": "勇敢的战士", "target_property": "rotation_euler", "animation_type": "rotation", "keyframes": [{"frame": 37, "value": [0.0, 0.0, 0.0], "interpolation": "LINEAR", "handle_left": null, "handle_right": null}, {"frame": 42, "value": [1.2566370614359172, 0.0, 0.0], "interpolation": "EASE_OUT", "handle_left": null, "handle_right": null}, {"frame": 56, "value": [0.0, 0.0, 0.0], "interpolation": "EASE_IN", "handle_left": null, "handle_right": null}], "bone_name": "RightArm"}], "markers": []}], "export_settings": {"export_format": "FBX", "export_path": "demo_warrior_1.fbx", "include_animations": true, "include_armatures": true, "include_meshes": true, "fbx_version": "7.4.0", "scale_factor": 1.0}, "metadata": {"source": "MotionAgent", "actions_count": 2, "total_frames": 56, "character": {"type": "warrior", "name": "勇敢的战士", "description": "强壮的战士角色"}, "character_config": {"type": "warrior", "display_name": "战士", "height": 1.9, "animation_features": {"has_facial_expressions": true, "has_hand_gestures": true, "walking_style": "heavy", "jump_style": "powerful"}}}}