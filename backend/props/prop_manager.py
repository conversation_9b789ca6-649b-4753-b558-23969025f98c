#!/usr/bin/env python3
"""
道具管理系统
根据设计思路实现道具识别、分类和绑定功能
"""

import json
from typing import Dict, List, Any, Optional
from pathlib import Path

from backend.utils.file import load_json_file


class PropManager:
    """道具管理器"""
    
    def __init__(self):
        """初始化道具管理器"""
        self.prop_types = self._load_prop_types()
        self.prop_templates = self._load_prop_templates()
    
    def _load_prop_types(self) -> List[Dict[str, Any]]:
        """加载道具类型定义"""
        try:
            return load_json_file("backend/props/prop_types.json")
        except FileNotFoundError:
            # 返回默认道具类型
            return [
                {
                    "name": "weapon",
                    "display_name": "武器",
                    "subcategories": ["sword", "bow", "spear", "gun", "magic_staff"],
                    "attachment_points": ["right_hand", "left_hand", "back", "belt"]
                },
                {
                    "name": "shield",
                    "display_name": "盾牌",
                    "subcategories": ["round_shield", "tower_shield", "buckler"],
                    "attachment_points": ["left_hand", "back"]
                },
                {
                    "name": "tool",
                    "display_name": "工具",
                    "subcategories": ["hammer", "pickaxe", "shovel", "rope"],
                    "attachment_points": ["right_hand", "left_hand", "belt"]
                },
                {
                    "name": "accessory",
                    "display_name": "饰品",
                    "subcategories": ["ring", "necklace", "crown", "cloak"],
                    "attachment_points": ["finger", "neck", "head", "shoulders"]
                }
            ]
    
    def _load_prop_templates(self) -> Dict[str, Dict[str, Any]]:
        """加载道具模板"""
        try:
            templates = load_json_file("backend/props/prop_templates.json")
            return {template["name"]: template for template in templates}
        except FileNotFoundError:
            # 返回默认模板
            return {
                "sword": {
                    "name": "sword",
                    "display_name": "剑",
                    "type": "weapon",
                    "subtype": "sword",
                    "default_attachment": "right_hand",
                    "properties": {
                        "length": 1.0,
                        "weight": 1.5,
                        "material": "steel",
                        "damage_type": "slash"
                    }
                },
                "shield": {
                    "name": "shield",
                    "display_name": "盾牌",
                    "type": "shield",
                    "subtype": "round_shield",
                    "default_attachment": "left_hand",
                    "properties": {
                        "diameter": 0.6,
                        "weight": 2.0,
                        "material": "wood_metal",
                        "defense_type": "block"
                    }
                },
                "bow": {
                    "name": "bow",
                    "display_name": "弓",
                    "type": "weapon",
                    "subtype": "bow",
                    "default_attachment": "left_hand",
                    "properties": {
                        "length": 1.2,
                        "weight": 0.8,
                        "material": "wood",
                        "damage_type": "pierce"
                    }
                }
            }
    
    def identify_props_from_text(self, text: str) -> List[Dict[str, Any]]:
        """从文本中识别道具"""
        identified_props = []
        prop_id_counter = 1
        
        # 道具关键词映射
        prop_keywords = {
            "剑": "sword",
            "sword": "sword",
            "刀": "sword",
            "盾牌": "shield",
            "shield": "shield",
            "盾": "shield",
            "弓": "bow",
            "bow": "bow",
            "箭": "arrow",
            "枪": "spear",
            "spear": "spear",
            "法杖": "magic_staff",
            "staff": "magic_staff",
            "锤子": "hammer",
            "hammer": "hammer"
        }
        
        # 在文本中搜索道具关键词
        for keyword, prop_name in prop_keywords.items():
            if keyword in text:
                if prop_name in self.prop_templates:
                    template = self.prop_templates[prop_name]
                    prop_info = {
                        "id": f"{prop_name}_{prop_id_counter:02d}",
                        "name": template["display_name"],
                        "type": template["type"],
                        "subtype": template["subtype"],
                        "template_name": prop_name,
                        "owner_id": "character",  # 默认属于主角色
                        "attachment_point": template["default_attachment"],
                        "initial_position": [0, 0, 0],
                        "properties": template["properties"].copy()
                    }
                    identified_props.append(prop_info)
                    prop_id_counter += 1
        
        return identified_props
    
    def get_prop_template(self, prop_name: str) -> Optional[Dict[str, Any]]:
        """获取道具模板"""
        return self.prop_templates.get(prop_name)
    
    def get_prop_types(self) -> List[Dict[str, Any]]:
        """获取所有道具类型"""
        return self.prop_types
    
    def validate_prop_attachment(self, prop_type: str, attachment_point: str) -> bool:
        """验证道具附着点是否有效"""
        for prop_type_def in self.prop_types:
            if prop_type_def["name"] == prop_type:
                return attachment_point in prop_type_def["attachment_points"]
        return False
    
    def suggest_attachment_point(self, prop_name: str, character_type: str = "human_male") -> str:
        """建议道具附着点"""
        if prop_name in self.prop_templates:
            template = self.prop_templates[prop_name]
            return template["default_attachment"]
        
        # 基于道具类型的默认建议
        if "sword" in prop_name or "刀" in prop_name:
            return "right_hand"
        elif "shield" in prop_name or "盾" in prop_name:
            return "left_hand"
        elif "bow" in prop_name or "弓" in prop_name:
            return "left_hand"
        else:
            return "right_hand"
    
    def create_prop_binding_instructions(self, props: List[Dict[str, Any]], 
                                       character_id: str) -> List[Dict[str, Any]]:
        """创建道具绑定指令"""
        binding_instructions = []
        
        for prop in props:
            instruction = {
                "type": "attach_prop",
                "prop_id": prop["id"],
                "character_id": character_id,
                "attachment_point": prop["attachment_point"],
                "position_offset": [0, 0, 0],
                "rotation_offset": [0, 0, 0],
                "scale": [1, 1, 1]
            }
            binding_instructions.append(instruction)
        
        return binding_instructions


# 全局道具管理器实例
prop_manager = PropManager()
