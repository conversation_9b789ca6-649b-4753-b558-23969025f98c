[{"name": "sword", "display_name": "剑", "display_name_en": "Sword", "type": "weapon", "subtype": "sword", "default_attachment": "right_hand", "model_path": "models/weapons/sword_basic.fbx", "properties": {"length": 1.0, "weight": 1.5, "material": "steel", "damage_type": "slash", "durability": 100, "rarity": "common"}, "animation_data": {"swing_duration": 0.8, "thrust_duration": 0.6, "block_duration": 0.4}}, {"name": "shield", "display_name": "盾牌", "display_name_en": "Shield", "type": "shield", "subtype": "round_shield", "default_attachment": "left_hand", "model_path": "models/shields/round_shield.fbx", "properties": {"diameter": 0.6, "weight": 2.0, "material": "wood_metal", "defense_type": "block", "durability": 150, "rarity": "common"}, "animation_data": {"block_duration": 0.5, "bash_duration": 0.7}}, {"name": "bow", "display_name": "弓", "display_name_en": "Bow", "type": "weapon", "subtype": "bow", "default_attachment": "left_hand", "model_path": "models/weapons/longbow.fbx", "properties": {"length": 1.2, "weight": 0.8, "material": "wood", "damage_type": "pierce", "range": 50.0, "durability": 80, "rarity": "common"}, "animation_data": {"draw_duration": 1.0, "release_duration": 0.3}}, {"name": "spear", "display_name": "长矛", "display_name_en": "Spear", "type": "weapon", "subtype": "spear", "default_attachment": "right_hand", "model_path": "models/weapons/spear.fbx", "properties": {"length": 2.0, "weight": 2.5, "material": "wood_metal", "damage_type": "pierce", "reach": 2.0, "durability": 120, "rarity": "common"}, "animation_data": {"thrust_duration": 0.8, "sweep_duration": 1.2}}, {"name": "magic_staff", "display_name": "法杖", "display_name_en": "Magic Staff", "type": "weapon", "subtype": "magic_staff", "default_attachment": "right_hand", "model_path": "models/weapons/magic_staff.fbx", "properties": {"length": 1.5, "weight": 1.0, "material": "wood_crystal", "damage_type": "magic", "mana_boost": 20, "durability": 60, "rarity": "rare"}, "animation_data": {"cast_duration": 1.5, "channel_duration": 2.0}}, {"name": "dagger", "display_name": "匕首", "display_name_en": "<PERSON>gger", "type": "weapon", "subtype": "dagger", "default_attachment": "belt", "model_path": "models/weapons/dagger.fbx", "properties": {"length": 0.3, "weight": 0.5, "material": "steel", "damage_type": "pierce", "speed_bonus": 0.3, "durability": 70, "rarity": "common"}, "animation_data": {"stab_duration": 0.4, "slash_duration": 0.5}}, {"name": "hammer", "display_name": "锤子", "display_name_en": "Hammer", "type": "tool", "subtype": "hammer", "default_attachment": "right_hand", "model_path": "models/tools/hammer.fbx", "properties": {"length": 0.8, "weight": 3.0, "material": "wood_metal", "damage_type": "blunt", "durability": 200, "rarity": "common"}, "animation_data": {"swing_duration": 1.0, "slam_duration": 1.2}}, {"name": "torch", "display_name": "火把", "display_name_en": "<PERSON>ch", "type": "tool", "subtype": "torch", "default_attachment": "left_hand", "model_path": "models/tools/torch.fbx", "properties": {"length": 0.5, "weight": 0.8, "material": "wood", "light_radius": 5.0, "burn_time": 300, "durability": 30, "rarity": "common"}, "animation_data": {"wave_duration": 0.6}}, {"name": "potion", "display_name": "药水", "display_name_en": "Potion", "type": "magical", "subtype": "potion", "default_attachment": "belt", "model_path": "models/items/potion.fbx", "properties": {"volume": 0.2, "weight": 0.3, "material": "glass_liquid", "effect_type": "healing", "potency": 50, "uses": 1, "rarity": "uncommon"}, "animation_data": {"drink_duration": 2.0}}, {"name": "crystal", "display_name": "水晶", "display_name_en": "Crystal", "type": "magical", "subtype": "crystal", "default_attachment": "left_hand", "model_path": "models/items/crystal.fbx", "properties": {"size": 0.1, "weight": 0.5, "material": "crystal", "magic_type": "energy", "power_level": 100, "durability": 50, "rarity": "rare"}, "animation_data": {"glow_duration": 3.0, "pulse_duration": 1.0}}]