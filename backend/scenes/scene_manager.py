#!/usr/bin/env python3
"""
场景管理系统
根据设计思路实现场景识别、环境设置和氛围控制
"""

import json
from typing import Dict, List, Any, Optional
from pathlib import Path

from backend.utils.file import load_json_file


class SceneManager:
    """场景管理器"""
    
    def __init__(self):
        """初始化场景管理器"""
        self.scene_types = self._load_scene_types()
        self.environment_presets = self._load_environment_presets()
    
    def _load_scene_types(self) -> List[Dict[str, Any]]:
        """加载场景类型定义"""
        try:
            return load_json_file("backend/scenes/scene_types.json")
        except FileNotFoundError:
            # 返回默认场景类型
            return [
                {
                    "name": "battlefield",
                    "display_name": "战场",
                    "description": "战斗场景",
                    "lighting": "dramatic",
                    "weather": "variable",
                    "terrain": "rough"
                },
                {
                    "name": "forest",
                    "display_name": "森林",
                    "description": "自然森林环境",
                    "lighting": "filtered",
                    "weather": "mild",
                    "terrain": "natural"
                },
                {
                    "name": "castle",
                    "display_name": "城堡",
                    "description": "中世纪城堡",
                    "lighting": "indoor",
                    "weather": "sheltered",
                    "terrain": "stone"
                }
            ]
    
    def _load_environment_presets(self) -> Dict[str, Dict[str, Any]]:
        """加载环境预设"""
        try:
            presets = load_json_file("backend/scenes/environment_presets.json")
            return {preset["name"]: preset for preset in presets}
        except FileNotFoundError:
            # 返回默认预设
            return {
                "daylight": {
                    "name": "daylight",
                    "display_name": "白天",
                    "lighting": {
                        "sun_intensity": 1.0,
                        "sun_angle": 45,
                        "ambient_color": [0.4, 0.4, 0.5],
                        "sun_color": [1.0, 0.95, 0.8]
                    },
                    "atmosphere": {
                        "fog_density": 0.1,
                        "sky_color": [0.5, 0.7, 1.0],
                        "cloud_coverage": 0.3
                    }
                },
                "sunset": {
                    "name": "sunset",
                    "display_name": "黄昏",
                    "lighting": {
                        "sun_intensity": 0.8,
                        "sun_angle": 15,
                        "ambient_color": [0.6, 0.4, 0.3],
                        "sun_color": [1.0, 0.6, 0.3]
                    },
                    "atmosphere": {
                        "fog_density": 0.2,
                        "sky_color": [1.0, 0.5, 0.3],
                        "cloud_coverage": 0.5
                    }
                },
                "night": {
                    "name": "night",
                    "display_name": "夜晚",
                    "lighting": {
                        "sun_intensity": 0.1,
                        "sun_angle": -30,
                        "ambient_color": [0.1, 0.1, 0.2],
                        "moon_color": [0.8, 0.8, 1.0]
                    },
                    "atmosphere": {
                        "fog_density": 0.3,
                        "sky_color": [0.1, 0.1, 0.3],
                        "cloud_coverage": 0.7
                    }
                }
            }
    
    def analyze_scene_from_text(self, text: str) -> Dict[str, Any]:
        """从文本中分析场景信息"""
        scene_info = {
            "description": "默认场景",
            "environment": "neutral",
            "lighting": "daylight",
            "weather": "clear",
            "terrain": "flat",
            "atmosphere": "normal",
            "props": [],
            "background_elements": []
        }
        
        # 场景关键词映射
        scene_keywords = {
            # 地点
            "战场": {"environment": "battlefield", "lighting": "dramatic", "atmosphere": "tense"},
            "battlefield": {"environment": "battlefield", "lighting": "dramatic", "atmosphere": "tense"},
            "森林": {"environment": "forest", "lighting": "filtered", "atmosphere": "peaceful"},
            "forest": {"environment": "forest", "lighting": "filtered", "atmosphere": "peaceful"},
            "城堡": {"environment": "castle", "lighting": "indoor", "atmosphere": "formal"},
            "castle": {"environment": "castle", "lighting": "indoor", "atmosphere": "formal"},
            "房间": {"environment": "indoor", "lighting": "indoor", "atmosphere": "intimate"},
            "室内": {"environment": "indoor", "lighting": "indoor", "atmosphere": "intimate"},
            "户外": {"environment": "outdoor", "lighting": "daylight", "atmosphere": "open"},
            "山": {"environment": "mountain", "lighting": "bright", "terrain": "rocky"},
            "海边": {"environment": "beach", "lighting": "bright", "atmosphere": "relaxed"},
            "沙漠": {"environment": "desert", "lighting": "harsh", "terrain": "sandy"},
            
            # 时间
            "白天": {"lighting": "daylight"},
            "daylight": {"lighting": "daylight"},
            "黄昏": {"lighting": "sunset"},
            "sunset": {"lighting": "sunset"},
            "夜晚": {"lighting": "night"},
            "night": {"lighting": "night"},
            "清晨": {"lighting": "dawn"},
            "dawn": {"lighting": "dawn"},
            
            # 天气
            "晴朗": {"weather": "clear"},
            "clear": {"weather": "clear"},
            "下雨": {"weather": "rain"},
            "rain": {"weather": "rain"},
            "雪": {"weather": "snow"},
            "snow": {"weather": "snow"},
            "雾": {"weather": "fog"},
            "fog": {"weather": "fog"},
            "风": {"weather": "windy"},
            "windy": {"weather": "windy"},
            
            # 氛围
            "紧张": {"atmosphere": "tense"},
            "tense": {"atmosphere": "tense"},
            "平静": {"atmosphere": "peaceful"},
            "peaceful": {"atmosphere": "peaceful"},
            "神秘": {"atmosphere": "mysterious"},
            "mysterious": {"atmosphere": "mysterious"},
            "欢乐": {"atmosphere": "joyful"},
            "joyful": {"atmosphere": "joyful"}
        }
        
        # 分析文本中的场景关键词
        for keyword, attributes in scene_keywords.items():
            if keyword in text:
                scene_info.update(attributes)
        
        # 设置描述
        if scene_info["environment"] != "neutral":
            scene_info["description"] = f"{scene_info['environment']}环境，{scene_info['lighting']}光照，{scene_info['weather']}天气"
        
        return scene_info
    
    def get_environment_preset(self, preset_name: str) -> Optional[Dict[str, Any]]:
        """获取环境预设"""
        return self.environment_presets.get(preset_name)
    
    def get_scene_types(self) -> List[Dict[str, Any]]:
        """获取所有场景类型"""
        return self.scene_types
    
    def create_blender_scene_setup(self, scene_info: Dict[str, Any]) -> Dict[str, Any]:
        """创建Blender场景设置"""
        lighting_preset = scene_info.get("lighting", "daylight")
        environment_data = self.get_environment_preset(lighting_preset)
        
        if not environment_data:
            environment_data = self.environment_presets["daylight"]
        
        blender_setup = {
            "world_settings": {
                "background_color": environment_data["atmosphere"]["sky_color"],
                "ambient_strength": 0.3
            },
            "lighting": {
                "sun_lamp": {
                    "energy": environment_data["lighting"]["sun_intensity"],
                    "angle": environment_data["lighting"]["sun_angle"],
                    "color": environment_data["lighting"]["sun_color"]
                },
                "ambient": {
                    "color": environment_data["lighting"]["ambient_color"],
                    "energy": 0.5
                }
            },
            "atmosphere": {
                "fog": {
                    "enabled": environment_data["atmosphere"]["fog_density"] > 0,
                    "density": environment_data["atmosphere"]["fog_density"],
                    "color": [0.7, 0.7, 0.8]
                }
            },
            "camera": {
                "position": [5, -5, 3],
                "rotation": [60, 0, 45],
                "focal_length": 50
            }
        }
        
        return blender_setup
    
    def suggest_camera_angles(self, scene_type: str, action_types: List[str]) -> List[Dict[str, Any]]:
        """根据场景和动作类型建议摄像机角度"""
        camera_suggestions = []
        
        # 基于动作类型的摄像机建议
        if "attack" in action_types or "defend" in action_types:
            camera_suggestions.append({
                "name": "combat_view",
                "position": [3, -4, 2],
                "rotation": [70, 0, 30],
                "description": "战斗视角"
            })
        
        if "jump" in action_types or "move" in action_types:
            camera_suggestions.append({
                "name": "action_view",
                "position": [6, -6, 4],
                "rotation": [60, 0, 45],
                "description": "动作视角"
            })
        
        if "gesture" in action_types or "emotion" in action_types:
            camera_suggestions.append({
                "name": "close_up",
                "position": [2, -3, 1.5],
                "rotation": [80, 0, 20],
                "description": "特写视角"
            })
        
        # 默认视角
        if not camera_suggestions:
            camera_suggestions.append({
                "name": "default_view",
                "position": [5, -5, 3],
                "rotation": [60, 0, 45],
                "description": "默认视角"
            })
        
        return camera_suggestions


# 全局场景管理器实例
scene_manager = SceneManager()
